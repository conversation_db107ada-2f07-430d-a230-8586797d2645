"use client";

import { useDraggable } from "@dnd-kit/core";
import { useState } from "react"; // Not used
import { useSortable } from "@dnd-kit/sortable"; // Not used
import { CSS } from "@dnd-kit/utilities";

export default function TasksCard({ task }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useDraggable({
    id: task.id,
  });
  const style = transform
    ? {
        transform: `translate(${transform.x}px, ${transform.y}px)`,
        opacity: isDragging ? 0.5 : 1, // Make the item semi-transparent while dragging
        boxShadow: isDragging ? "10px 10px 10px -3px rgba(255, 255, 255, 0.7)" : "none", // Add a shadow when dragging
        zIndex: isDragging ? 999 : "auto", // Ensure the dragged item is on top
        dropShadow: isDragging ? "10px 10px 15px rgba(255, 255, 255, 0.7)" : "none",
        transition,
      }
    : undefined;
  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={style}
      className="cursor-grab rounded-lg bg-neutral-700 p-4 shadow-sm hover:shadow-md"
    >
      <h3 className="text-sm font-semibold text-white">{task.title}</h3>
      <p className="mt-2 text-xs text-neutral-400">{task.description}</p>
    </div>
  );
}
