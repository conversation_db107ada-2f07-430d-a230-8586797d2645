"use client";

import { DndContext, closestCorners } from "@dnd-kit/core";
import { SortableContext, arrayMove, verticalListSortingStrategy } from "@dnd-kit/sortable"; // Import SortableContext and arrayMove
import { useState } from "react";

// =======================================
import Columns from "@/components/Columns";

// =======================================
const COLUMNS = [
  { id: "TODO", title: "To Do" },
  { id: "IN_PROGRESS", title: "In Progress" },
  { id: "DONE", title: "Done" },
];

const INITIAL_TASKS = [
  {
    id: "1",
    title: "Research Project",
    description: "Gather requirements and create initial documentation",
    status: "TODO",
  },
  {
    id: "2",
    title: "Design System",
    description: "Create component library and design tokens",
    status: "TODO",
  },
  {
    id: "3",
    title: "API Integration",
    description: "Implement REST API endpoints",
    status: "IN_PROGRESS",
  },
  {
    id: "4",
    title: "Testing",
    description: "Write unit tests for core functionality",
    status: "DONE",
  },
];

export default function LearnDragnDrop({ params }) {
  const [tasks, setTasks] = useState(INITIAL_TASKS);
  // Define a function to handle the end of a drag operation. This function will be called when a dragged item is dropped.
  function handleDragEnd(event) {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const activeId = active.id;
    const overId = over.id;

    setTasks((currentTasks) => {
      const activeTask = currentTasks.find((t) => t.id === activeId);
      if (!activeTask) return currentTasks;

      // Check if we are dropping on a sortable item (a task) or a droppable container (a column).
      const isOverSortableItem = over.data.current?.sortable;

      // --- SCENARIO 1: REORDERING WITHIN THE SAME COLUMN ---
      // This is the most specific case: we are dropping a task onto another task in the same column.
      if (isOverSortableItem && activeTask.status === over.data.current.sortable.containerId) {
        const oldIndex = currentTasks.findIndex((t) => t.id === activeId);
        const newIndex = currentTasks.findIndex((t) => t.id === overId);

        if (oldIndex !== -1 && newIndex !== -1) {
          return arrayMove(currentTasks, oldIndex, newIndex);
        }
      }

      // --- SCENARIO 2: MOVING TO A DIFFERENT COLUMN ---
      // This handles dropping a task on another task in a different column OR on the column container itself.
      const overContainerId = over.data.current?.sortable?.containerId || over.id;
      const isAValidColumn = COLUMNS.some((c) => c.id === overContainerId);

      if (isAValidColumn && activeTask.status !== overContainerId) {
        return currentTasks.map((task) => {
          if (task.id === activeId) {
            return { ...task, status: overContainerId };
          }
          return task;
        });
      }

      // If neither of the above conditions are met, do not update the state.
      return currentTasks;
    });
  }

  return (
    <>
      <div className="p-4">
        <div className="flex gap-8">
          <DndContext onDragEnd={handleDragEnd} collisionDetection={closestCorners}>
            {COLUMNS.map((column) => (
              // Each column is a SortableContext, managing the sortable tasks within it.
              // The `items` prop should be an array of unique IDs of the sortable items in this column.
              <SortableContext key={column.id} items={tasks.filter(task => task.status === column.id).map(task => task.id)} strategy={verticalListSortingStrategy}>
                <Columns column={column} tasks={tasks.filter((task) => task.status === column.id)} />
              </SortableContext>
            ))}
          </DndContext>
        </div>
      </div>
    </>
  );
}
