"use client";
import { useDroppable } from "@dnd-kit/core";
import { useSortable } from "@dnd-kit/sortable"; // Import useSortable
import TasksCard from "./TasksCard";

// It's a best practice to define components outside of other components
// to prevent them from being re-created on every render.
function SortableTask({ task }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: task.id });

  const style = {
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
    transition,
    // Add a visual effect for when an item is being dragged
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <TasksCard task={task} />
    </div>
  );
}

export default function Columns({ column, tasks }) {
  const { setNodeRef } = useDroppable({
    id: column.id,
  });

  return (
    <div ref={setNodeRef} className="w-72 bg-neutral-800 p-4 rounded-lg shadow-inner min-h-[100px]">
      <h2 className="text-lg font-bold mb-4 text-neutral-100">{column.title}</h2>
      <div className="space-y-4">
        {tasks.map((task) => (
          <SortableTask key={task.id} task={task} />
        ))}
      </div>
    </div>
  );
}
